@echo off
setlocal enabledelayedexpansion

echo 🚀 Neva Termal Escape - Caprover Deployment başlatılıyor...

REM Caprover CLI kurulu mu kontrol et
where caprover >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Caprover CLI bulunamadı!
    echo Lütfen önce Caprover CLI'yi kurun:
    echo npm install -g caprover
    pause
    exit /b 1
)

echo ✅ Caprover CLI bulundu

REM Build işlemi
echo 📦 Proje build ediliyor...
call npm run build

if %errorlevel% neq 0 (
    echo ❌ Build işlemi başarısız!
    pause
    exit /b 1
)

echo ✅ Build başarılı

REM Caprover'a deploy et
echo 🚢 Caprover'a deploy ediliyor...

REM caprover.json yoksa oluştur
if not exist "caprover.json" (
    echo caprover.json bulunamadı. Konfigürasyon oluşturuluyor...
    set /p server_url="Caprover server URL'nizi girin: "
    set /p app_name="App adınızı girin: "
    
    echo {> caprover.json
    echo   "schemaVersion": 2,>> caprover.json
    echo   "appName": "!app_name!",>> caprover.json
    echo   "branch": "main">> caprover.json
    echo }>> caprover.json
    
    echo ✅ caprover.json oluşturuldu
)

REM Deploy işlemi
call caprover deploy

if %errorlevel% equ 0 (
    echo 🎉 Deployment başarılı!
    echo Uygulamanız artık Caprover'da çalışıyor.
) else (
    echo ❌ Deployment başarısız!
    echo Lütfen Caprover konfigürasyonunuzu kontrol edin.
)

pause
