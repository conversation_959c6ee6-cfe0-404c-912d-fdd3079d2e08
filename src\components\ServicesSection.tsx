import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Waves, Home, UtensilsCrossed, Users } from "lucide-react";
import thermalSpaImage from "@/assets/spa-center.jpg";
import roomThermalImage from "@/assets/thermal-pool-room.jpg";
import restaurantImage from "@/assets/restaurant.jpg";
import hotelLobbyImage from "@/assets/hotel-lobby.jpg";

const ServicesSection = () => {
  const services = [
    {
      icon: <Waves className="w-8 h-8" />,
      title: "Termal Sağlık & Spa Deneyimi",
      description: "74°C şifalı termal sularla romatizma, eklem ağrıları ve stres tedavisi",
      features: ["Kapalı termal havuzlar", "Bay-bayan ayrı alanlar", "Sauna & Türk Hamamı", "Profesyonel spa"],
      image: thermalSpaImage
    },
    {
      icon: <Home className="w-8 h-8" />,
      title: "Özel Havuzlu 1+1 Daireler",
      description: "65 m² modern konaklama alanları, özel termal havuzlu",
      features: ["2.5-3 ton özel termal havuz", "Türk hamamı (kurna)", "Mini mutfak & salon", "Balkon & oda servisi"],
      image: roomThermalImage
    },
    {
      icon: <UtensilsCrossed className="w-8 h-8" />,
      title: "Lezzetli Yeme İçme",
      description: "Yarım pansiyon plus konsepti ile sağlıklı beslenme",
      features: ["Açık büfe kahvaltı", "Öğle yemeği (alakart)", "Çay saati ikramları", "Akşam yemeği büfe"],
      image: restaurantImage
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Aile Dostu Aktiviteler",
      description: "Çocuklar için güvenli alanlar ve aile aktiviteleri",
      features: ["Çocuk havuzları", "Güvenli oyun alanları", "Aile odaları", "Sosyal aktivite alanları"],
      image: hotelLobbyImage
    }
  ];

  return (
    <section id="hizmetler" className="py-20 bg-spa">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4">
            Hizmetlerimiz
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-primary mb-6">
            Premium Termal Tatil Deneyimi
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Futbol temalı tasarımımız ve şifalı termal sularımızla sağlık, konfor ve eğlenceyi bir araya getiriyoruz.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {services.map((service, index) => (
            <Card key={index} className="group hover:shadow-luxury transition-all duration-300 hover:scale-105 bg-card border-0 shadow-lg">
              {service.image && (
                <div className="relative h-48 overflow-hidden rounded-t-lg">
                  <img 
                    src={service.image} 
                    alt={service.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-primary/60"></div>
                  <div className="absolute top-4 left-4 text-accent bg-white/20 rounded-full p-3 backdrop-blur-sm">
                    {service.icon}
                  </div>
                </div>
              )}
              
              <CardHeader className={!service.image ? "pt-8" : ""}>
                {!service.image && (
                  <div className="text-primary mb-4">
                    {service.icon}
                  </div>
                )}
                <CardTitle className="text-2xl text-primary">{service.title}</CardTitle>
                <CardDescription className="text-base">{service.description}</CardDescription>
              </CardHeader>
              
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {service.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-accent rounded-full mr-3"></div>
                      {feature}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;