import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MapPin, Phone, Mail, Star } from "lucide-react";
import heroImage from "@/assets/hotel-exterior.jpg";
import logoImage from "@/assets/logo.png";

const HotelHero = () => {
  const scrollToContact = () => {
    document.getElementById('iletisim')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section id="anasayfa" className="relative min-h-screen flex items-center justify-center overflow-hidden pt-20">{/* Added pt-20 for header space */}
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${heroImage})` }}
      >
        <div className="absolute inset-0 bg-primary/80"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center px-4 max-w-6xl mx-auto">
        {/* Logo */}
        <div className="mb-8">
          <img 
            src={logoImage} 
            alt="Grand Neva Oteli Logo" 
            className="h-24 w-24 mx-auto mb-4 rounded-full shadow-luxury"
          />
          <Badge variant="secondary" className="text-lg px-6 py-2 mb-4">
            <Star className="w-4 h-4 mr-1" />
            Afyon'un En Lüks Termal Oteli
          </Badge>
        </div>

        {/* Main Title */}
        <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
          GRAND NEVA
          <span className="block text-accent">TERMAL OTEL</span>
        </h1>

        {/* Subtitle */}
        <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed">
          Şifanın Kalbinde: Futbol Temalı Termal Spa Deneyimi
          <span className="block text-accent font-semibold mt-2">
            Gazlıgöl'ün Şifalı Sularında Yenilenin
          </span>
        </p>

        {/* Contact Info */}
        <div className="flex flex-wrap justify-center gap-4 mb-8 text-white">
          <div className="flex items-center gap-2">
            <MapPin className="w-5 h-5 text-accent" />
            <span className="text-sm">Gazlıgöl, Afyonkarahisar</span>
          </div>
          <div className="flex items-center gap-2">
            <Phone className="w-5 h-5 text-accent" />
            <span className="text-sm">0272 772 08 42</span>
          </div>
          <div className="flex items-center gap-2">
            <Mail className="w-5 h-5 text-accent" />
            <span className="text-sm"><EMAIL></span>
          </div>
        </div>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            variant="hero" 
            size="lg" 
            onClick={scrollToContact}
            className="text-lg px-8 py-6"
          >
            Hemen Rezervasyon Yap
          </Button>
          <Button 
            variant="luxury" 
            size="lg"
            onClick={() => document.getElementById('hizmetler')?.scrollIntoView({ behavior: 'smooth' })}
            className="text-lg px-8 py-6"
          >
            Hizmetlerimizi Keşfet
          </Button>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default HotelHero;