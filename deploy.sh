#!/bin/bash

# Neva Termal Escape - Caprover Deployment Script
# Bu script projenizi Caprover'a deploy eder

echo "🚀 Neva Termal Escape - Caprover Deployment başlatılıyor..."

# Renklendirme için
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Caprover CLI kurulu mu kontrol et
if ! command -v caprover &> /dev/null; then
    echo -e "${RED}❌ Caprover CLI bulunamadı!${NC}"
    echo -e "${YELLOW}Lütfen önce Caprover CLI'yi kurun:${NC}"
    echo "npm install -g caprover"
    exit 1
fi

echo -e "${GREEN}✅ Caprover CLI bulundu${NC}"

# Git durumunu kontrol et
if [[ -n $(git status --porcelain) ]]; then
    echo -e "${YELLOW}⚠️  Commit edilmemiş değişiklikler var!${NC}"
    echo -e "${BLUE}Devam etmek istiyor musunuz? (y/n)${NC}"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo -e "${RED}❌ Deployment iptal edildi${NC}"
        exit 1
    fi
fi

# Build işlemi
echo -e "${BLUE}📦 Proje build ediliyor...${NC}"
npm run build

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Build işlemi başarısız!${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Build başarılı${NC}"

# Caprover'a deploy et
echo -e "${BLUE}🚢 Caprover'a deploy ediliyor...${NC}"

# Eğer caprover.json yoksa, kullanıcıdan bilgileri al
if [ ! -f "caprover.json" ]; then
    echo -e "${YELLOW}caprover.json bulunamadı. Konfigürasyon oluşturuluyor...${NC}"
    echo -e "${BLUE}Caprover server URL'nizi girin:${NC}"
    read -r server_url
    echo -e "${BLUE}App adınızı girin:${NC}"
    read -r app_name
    
    cat > caprover.json << EOF
{
  "schemaVersion": 2,
  "appName": "$app_name",
  "branch": "main"
}
EOF
    echo -e "${GREEN}✅ caprover.json oluşturuldu${NC}"
fi

# Deploy işlemi
caprover deploy

if [ $? -eq 0 ]; then
    echo -e "${GREEN}🎉 Deployment başarılı!${NC}"
    echo -e "${BLUE}Uygulamanız artık Caprover'da çalışıyor.${NC}"
else
    echo -e "${RED}❌ Deployment başarısız!${NC}"
    echo -e "${YELLOW}Lütfen Caprover konfigürasyonunuzu kontrol edin.${NC}"
    exit 1
fi
