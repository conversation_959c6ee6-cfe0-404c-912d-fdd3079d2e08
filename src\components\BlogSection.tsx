import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar, Clock, ArrowRight } from "lucide-react";

const BlogSection = () => {
  const blogPosts = [
    {
      title: "Termalin Kalbinde Bir Tatil: Hem Sağlık Hem Konfor",
      description: "Gazlıgöl'ün Şifalı Termal Suları ile Evinizde Gibi Hissedeceğiniz Tatil Deneyimi.",
      excerpt: "74°C şifalı termal sularım<PERSON>zla romatizma, eklem ağrıları ve stresi geride bırakın. Modern dairelerimizde özel termal havuzunuzda dinlenirken, sağlıklı beslenme seçeneklerimizle vücudunuzu yenileyin.",
      readTime: "5 dk",
      date: "15 Ocak 2024",
      category: "Sağlık & Wellness"
    },
    {
      title: "Efsaneler Diyarında Şifalı Bir Mola",
      description: "Frig Vadisi'nin Eteklerinde Sağlık ve Tarihle İç İçe Bir Tatil: Grand Neva Termal Otel",
      excerpt: "Frigya Vadisi'nin kadim atmosferinde, Ayazini Kaya Yerleşimi ve Emre Gölü'ne yakın konumumuzda tarihi keşfederken termal sularda şifa bulun. Geçmişle bugünün buluştuğu eşsiz bir deneyim.",
      readTime: "7 dk",
      date: "12 Ocak 2024",
      category: "Tarih & Kültür"
    },
    {
      title: "Aile Sıcaklığı, Şifa ve Eğlence Bir Arada",
      description: "Ailece Tatil Planlayanlara: Çocuk Dostu Termal Villalarda Unutulmaz Bir Konaklama",
      excerpt: "Bay-bayan ayrı termal havuzlar, güvenli çocuk alanları ve aile dostu aktivitelerle tüm aile bireyleri için mükemmel bir tatil deneyimi. Mahremiyet ve konforun buluştuğu özel atmosfer.",
      readTime: "6 dk",
      date: "10 Ocak 2024",
      category: "Aile & Çocuk"
    }
  ];

  return (
    <section id="blog" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4">
            Blog
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-primary mb-6">
            Termal Tatil Rehberi
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Gazlıgöl'ün şifalı sularından Frigya Vadisi'nin tarihine, 
            termal tatil deneyiminizi zenginleştirecek bilgiler ve öneriler.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.map((post, index) => (
            <Card key={index} className="group hover:shadow-luxury transition-all duration-300 hover:scale-105 bg-card border-0 shadow-lg overflow-hidden">
              <div className="relative">
                <div className="absolute inset-0 bg-primary/20"></div>
                <div className="relative p-6 bg-primary text-white">
                  <Badge 
                    variant="outline" 
                    className="mb-3 border-white/30 text-white bg-white/20 backdrop-blur-sm"
                  >
                    {post.category}
                  </Badge>
                  <h3 className="text-xl font-bold mb-2 leading-tight">{post.title}</h3>
                  <p className="text-white/90 text-sm">{post.description}</p>
                </div>
              </div>

              <CardContent className="p-6">
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  {post.excerpt}
                </p>

                <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    <span>{post.date}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    <span>{post.readTime} okuma</span>
                  </div>
                </div>

                <Button 
                  variant="ghost" 
                  className="w-full group-hover:bg-thermal group-hover:text-thermal-foreground transition-all"
                >
                  Yazının Devamı
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* SEO Content Section */}
        <div className="mt-16 grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-primary">
              Afyon Termal Tatil Rehberi
            </h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-primary mb-2">Afyon Termal Tatili İçin Doğru Zaman Ne Zaman?</h4>
                <p className="text-muted-foreground text-sm">
                  Afyon'da termal tatil her mevsim yapılabilir; özellikle kış aylarında kar manzarasında sıcak termal havuz keyfi çok tercih edilir. 
                  Ancak yaz aylarında da serin iklimi sayesinde aileler için huzurlu bir tatil alternatifi oluşturur.
                </p>
              </div>

              <div>
                <h4 className="font-semibold text-primary mb-2">Gazlıgöl Kaplıca Otelleri</h4>
                <p className="text-muted-foreground text-sm">
                  Gazlıgöl mevkiinde bulunan ve termal kaynaklarla doğrudan bağlantısı olan otellerdir. 
                  Grand Neva Termal, Gazlıgöl'ün en zengin termal kaynaklarından beslenen özel bir tesistir.
                </p>
              </div>

              <div>
                <h4 className="font-semibold text-primary mb-2">Muhafazakar Aileye Uygun Termal Otel</h4>
                <p className="text-muted-foreground text-sm">
                  Kadın-erkek ayrı alanları, özel havuzları ve aileye uygun konseptiyle mahremiyet sunan tesislerdir. 
                  Grand Neva Termal, tam da bu beklentilere göre tasarlanmıştır.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-primary">
              Özel Konaklama Seçenekleri
            </h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-primary mb-2">Özel Havuzlu Daire Kiralık Gazlıgöl</h4>
                <p className="text-muted-foreground text-sm">
                  Günlük veya haftalık kiralanabilen, içerisinde özel termal havuz bulunan konaklama birimleridir. 
                  Grand Neva Termal'de tüm daireler bu konseptle hazırlanmıştır.
                </p>
              </div>

              <div>
                <h4 className="font-semibold text-primary mb-2">Çocukla Gidilecek Termal Oteller</h4>
                <p className="text-muted-foreground text-sm">
                  Ailelerin çocuklarıyla birlikte güvenle konaklayabileceği, eğlenceli ve sağlıklı tatil alanlarıdır. 
                  Grand Neva Termal'in çocuk dostu havuzları ve güvenli alanlarıyla gönül rahatlığı sağlanır.
                </p>
              </div>

              <div>
                <h4 className="font-semibold text-primary mb-2">Termal Tatilde Mahremiyet Arayanlara Özel</h4>
                <p className="text-muted-foreground text-sm">
                  Karma alanlar yerine özel kullanım alanı sunan tesislere yönelen misafirlerin tercihidir. 
                  Grand Neva Termal'de tüm odalar ve alanlar, maksimum mahremiyet ilkesiyle planlanmıştır.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-12 text-center">
          <Card className="bg-primary text-white p-8">
            <CardContent className="p-0">
              <h3 className="text-2xl font-bold mb-4">
                Termal Tatilinizi Şansa Bırakmayın: Şimdi Rezervasyon Yapın
              </h3>
              <p className="text-white/90 mb-6 max-w-3xl mx-auto">
                Arama motorlarında "Afyon en iyi termal otel", "Gazlıgöl çocukla kalınacak otel", 
                "özel havuzlu termal daire" gibi sorgularla yolculuğa çıkan misafirler için Grand Neva Termal Otel, 
                hem sunduğu olanaklar hem de misafir memnuniyeti ile ilk sıralarda yer almayı hak ediyor.
              </p>
              <Button 
                variant="luxury" 
                size="lg"
                onClick={() => document.getElementById('iletisim')?.scrollIntoView({ behavior: 'smooth' })}
              >
                Hemen Rezervasyon Yap
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default BlogSection;