import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { MapPin, Phone, Mail, Facebook, Instagram, MessageCircle } from "lucide-react";
import logoImage from "@/assets/logo.png";

const FooterSection = () => {
  const quickLinks = [
    { name: "<PERSON>", href: "#" },
    { name: "Hizmetler<PERSON><PERSON>", href: "#hizmet<PERSON>" },
    { name: "Odalarımız", href: "#odalar" },
    { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "#iletisim" }
  ];

  const facilities = [
    "Termal Spa & Wellness",
    "Özel Havuzlu Odalar", 
    "Türk Hamamı",
    "Çocuk Havuzları",
    "Yarım Pansiyon Plus",
    "24 Saat Oda Servisi"
  ];

  return (
    <footer className="bg-primary-dark text-white">
      {/* Main Footer */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Logo & Description */}
          <div className="lg:col-span-1">
            <div className="flex items-center gap-3 mb-6">
              <img 
                src={logoImage} 
                alt="Grand Neva Logo" 
                className="h-12 w-12 rounded-full"
              />
              <div>
                <h3 className="text-xl font-bold">GRAND NEVA</h3>
                <p className="text-accent text-sm">TERMAL OTEL</p>
              </div>
            </div>
            <p className="text-white/80 text-sm leading-relaxed mb-6">
              Afyon Gazlıgöl'de şifalı termal sularla buluşmanın en konforlu yolu. 
              Futbol temalı tasarımımız ve lüks hizmetlerimizle unutulmaz bir tatil deneyimi.
            </p>
            <div className="flex gap-3">
              <Button size="sm" variant="ghost" className="text-white hover:bg-white/20 p-2">
                <Facebook className="w-5 h-5" />
              </Button>
              <Button size="sm" variant="ghost" className="text-white hover:bg-white/20 p-2">
                <Instagram className="w-5 h-5" />
              </Button>
              <Button 
                size="sm" 
                variant="whatsapp" 
                className="p-2"
                onClick={() => window.open('https://wa.me/905533141063', '_blank')}
              >
                <MessageCircle className="w-5 h-5" />
              </Button>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-accent">Hızlı Erişim</h4>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href}
                    className="text-white/80 hover:text-accent transition-colors text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Facilities */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-accent">Olanaklar</h4>
            <ul className="space-y-3">
              {facilities.map((facility, index) => (
                <li key={index} className="text-white/80 text-sm flex items-center">
                  <div className="w-1.5 h-1.5 bg-accent rounded-full mr-3"></div>
                  {facility}
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-accent">İletişim Bilgileri</h4>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <MapPin className="w-5 h-5 text-accent mt-0.5 flex-shrink-0" />
                <div className="text-white/80 text-sm">
                  <p>ESENTEPE MAH. GÜNDOĞAN CAD.</p>
                  <p>NO: 5 YAYLA TERMAL</p>
                  <p>03370 İhsaniye/Afyonkarahisar</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Phone className="w-5 h-5 text-accent" />
                <a 
                  href="tel:+902727720842"
                  className="text-white/80 hover:text-accent transition-colors text-sm"
                >
                  +90 272 772 08 42
                </a>
              </div>

              <div className="flex items-center gap-3">
                <MessageCircle className="w-5 h-5 text-accent" />
                <a 
                  href="https://wa.me/905533141063"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-white/80 hover:text-accent transition-colors text-sm"
                >
                  0553 314 10 63 (WhatsApp)
                </a>
              </div>

              <div className="flex items-center gap-3">
                <Mail className="w-5 h-5 text-accent" />
                <a 
                  href="mailto:<EMAIL>"
                  className="text-white/80 hover:text-accent transition-colors text-sm"
                >
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-white/20">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-white/60 text-sm">
              © 2024 Grand Neva Termal Otel. Tüm hakları saklıdır.
            </div>
            <div className="flex items-center gap-4 text-white/60 text-sm">
              <span>Afyon Gazlıgöl'de Termal Tatilin Yeni Adı</span>
              <Badge variant="secondary" className="text-xs">
                Premium Termal Spa
              </Badge>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default FooterSection;