import J<PERSON>Zip from 'jszip';

export async function extractZipFile(zipFile: File | ArrayBuffer): Promise<{ [key: string]: string }> {
  const zip = new JSZip();
  const zipData = await zip.loadAsync(zipFile);
  const extractedFiles: { [key: string]: string } = {};

  for (const [filename, file] of Object.entries(zipData.files)) {
    if (!file.dir && (filename.toLowerCase().endsWith('.jpg') || 
                      filename.toLowerCase().endsWith('.jpeg') || 
                      filename.toLowerCase().endsWith('.png') || 
                      filename.toLowerCase().endsWith('.webp'))) {
      try {
        const blob = await file.async('blob');
        const dataUrl = await new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.readAsDataURL(blob);
        });
        extractedFiles[filename] = dataUrl;
      } catch (error) {
        console.error(`Error extracting file ${filename}:`, error);
      }
    }
  }

  return extractedFiles;
}

export async function downloadFileFromDataUrl(dataUrl: string, filename: string): Promise<Blob> {
  const response = await fetch(dataUrl);
  return response.blob();
}