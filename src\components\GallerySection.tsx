import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { useState } from "react";
import { X } from "lucide-react";

const GallerySection = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const galleryImages = [
    { src: "/assets/0a3b.jpg", alt: "Grand Neva Termal Otel", title: "Otel Görünümü" },
    { src: "/assets/0ab9.jpg", alt: "Grand Neva Termal Otel", title: "Otel Alanları" },
    { src: "/assets/1a22.jpg", alt: "Grand Neva Termal Otel", title: "Termal Alanlar" },
    { src: "/assets/1c04.jpg", alt: "Grand Neva Termal Otel", title: "Otel İç Mekan" },
    { src: "/assets/2ded.jpg", alt: "Grand Neva Termal Otel", title: "Spa Alanları" },
    { src: "/assets/3bd6.jpg", alt: "Grand Neva Termal Otel", title: "Wellness Merkezi" },
    { src: "/assets/7a2f.jpg", alt: "Grand Neva Termal Otel", title: "Termal Havuzlar" },
    { src: "/assets/8e83.jpg", alt: "Grand Neva Termal Otel", title: "Otel Odaları" },
    { src: "/assets/8e87.jpg", alt: "Grand Neva Termal Otel", title: "Lüks Konaklama" },
    { src: "/assets/9dcc.jpg", alt: "Grand Neva Termal Otel", title: "Restoran Alanı" },
    { src: "/assets/9e39.jpg", alt: "Grand Neva Termal Otel", title: "Yemek Salonu" },
    { src: "/assets/13d4.jpg", alt: "Grand Neva Termal Otel", title: "Otel Lobisi" },
    { src: "/assets/79ec.jpg", alt: "Grand Neva Termal Otel", title: "Resepsiyon" },
    { src: "/assets/096c.jpg", alt: "Grand Neva Termal Otel", title: "Dinlenme Alanı" },
    { src: "/assets/578a.jpg", alt: "Grand Neva Termal Otel", title: "Sosyal Alanlar" },
    { src: "/assets/0613.jpg", alt: "Grand Neva Termal Otel", title: "Termal Tedavi" },
    { src: "/assets/2042.jpg", alt: "Grand Neva Termal Otel", title: "Sağlık Merkezi" },
    { src: "/assets/4585.jpg", alt: "Grand Neva Termal Otel", title: "Masaj Odaları" },
    { src: "/assets/6464.jpg", alt: "Grand Neva Termal Otel", title: "Sauna" },
    { src: "/assets/8509.jpg", alt: "Grand Neva Termal Otel", title: "Türk Hamamı" },
    { src: "/assets/8829.jpg", alt: "Grand Neva Termal Otel", title: "Jakuzi" },
    { src: "/assets/9097.jpg", alt: "Grand Neva Termal Otel", title: "Fitness Salonu" },
    { src: "/assets/a2e3.jpg", alt: "Grand Neva Termal Otel", title: "Spor Alanları" },
    { src: "/assets/a42a.jpg", alt: "Grand Neva Termal Otel", title: "Açık Havuz" },
    { src: "/assets/a531.jpg", alt: "Grand Neva Termal Otel", title: "Kapalı Havuz" },
    { src: "/assets/ab14.jpg", alt: "Grand Neva Termal Otel", title: "Çocuk Havuzu" },
    { src: "/assets/b26b.jpg", alt: "Grand Neva Termal Otel", title: "Bahçe Alanları" },
    { src: "/assets/ba13.jpg", alt: "Grand Neva Termal Otel", title: "Peyzaj" },
    { src: "/assets/bb49.jpg", alt: "Grand Neva Termal Otel", title: "Doğal Alanlar" },
    { src: "/assets/bea5.jpg", alt: "Grand Neva Termal Otel", title: "Manzara" },
    { src: "/assets/c2b5.jpg", alt: "Grand Neva Termal Otel", title: "Balkon Manzarası" },
    { src: "/assets/c5fd.jpg", alt: "Grand Neva Termal Otel", title: "Oda Manzarası" },
    { src: "/assets/c244.jpg", alt: "Grand Neva Termal Otel", title: "Süit Odalar" },
    { src: "/assets/ce25.jpg", alt: "Grand Neva Termal Otel", title: "Deluxe Odalar" },
    { src: "/assets/d98e.jpg", alt: "Grand Neva Termal Otel", title: "Standart Odalar" },
    { src: "/assets/db05.jpg", alt: "Grand Neva Termal Otel", title: "Aile Odaları" },
    { src: "/assets/db33.jpg", alt: "Grand Neva Termal Otel", title: "Çift Kişilik Oda" },
    { src: "/assets/e2ff.jpg", alt: "Grand Neva Termal Otel", title: "Tek Kişilik Oda" },
    { src: "/assets/e37c.jpg", alt: "Grand Neva Termal Otel", title: "Banyo" },
    { src: "/assets/e42c.jpg", alt: "Grand Neva Termal Otel", title: "Oda İç Mekan" },
    { src: "/assets/e46c.jpg", alt: "Grand Neva Termal Otel", title: "Mobilyalar" },
    { src: "/assets/f2c4.jpg", alt: "Grand Neva Termal Otel", title: "Dekorasyon" },
    { src: "/assets/f229.jpg", alt: "Grand Neva Termal Otel", title: "İç Tasarım" },
    { src: "/assets/f964.jpg", alt: "Grand Neva Termal Otel", title: "Konfor Alanları" },
    { src: "/assets/fab9.jpg", alt: "Grand Neva Termal Otel", title: "Dinlendirici Ortam" },
    { src: "/assets/fb0a.jpg", alt: "Grand Neva Termal Otel", title: "Huzurlu Atmosfer" },
    { src: "/assets/fb97.jpg", alt: "Grand Neva Termal Otel", title: "Lüks Detaylar" },
    { src: "/assets/fe35.jpg", alt: "Grand Neva Termal Otel", title: "Özel Hizmetler" },
    { src: "/assets/ffff.jpg", alt: "Grand Neva Termal Otel", title: "Premium Deneyim" }
  ];

  return (
    <section id="galeri" className="py-20 bg-spa">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4">
            Galeri
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-primary mb-6">
            Grand Neva'yı Keşfedin
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Lüks konaklamanız, şifalı termal deneyiminiz ve unutulmaz anlarınızdan kareler
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {galleryImages.map((image, index) => (
            <Card 
              key={index} 
              className="group cursor-pointer overflow-hidden hover:shadow-luxury transition-all duration-300 hover:scale-105"
              onClick={() => setSelectedImage(image.src)}
            >
              <div className="relative h-64 overflow-hidden">
                <img 
                  src={image.src}
                  alt={image.alt}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />

                <div className="absolute bottom-4 left-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <h4 className="font-semibold text-sm">{image.title}</h4>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Lightbox Modal */}
        {selectedImage && (
          <div 
            className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedImage(null)}
          >
            <div className="relative max-w-4xl max-h-full">
              <button
                className="absolute top-4 right-4 text-white hover:text-accent transition-colors z-10"
                onClick={() => setSelectedImage(null)}
              >
                <X className="w-8 h-8" />
              </button>
              <img 
                src={selectedImage}
                alt="Gallery Image"
                className="max-w-full max-h-full object-contain rounded-lg"
              />
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default GallerySection;