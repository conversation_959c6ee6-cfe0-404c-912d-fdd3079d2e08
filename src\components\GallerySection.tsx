import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { useState } from "react";
import { X } from "lucide-react";
import hotelExteriorImage from "@/assets/hotel-exterior.jpg";
import thermalPoolRoomImage from "@/assets/thermal-pool-room.jpg";
import spaCenterImage from "@/assets/spa-center.jpg";
import restaurantImage from "@/assets/restaurant.jpg";
import hotelLobbyImage from "@/assets/hotel-lobby.jpg";
import outdoorThermalImage from "@/assets/outdoor-thermal.jpg";

const GallerySection = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const galleryImages = [
    {
      src: hotelExteriorImage,
      alt: "Grand Neva Termal Otel Dış Görünüm",
      title: "Otel Dış Görünümü"
    },
    {
      src: thermalPoolRoomImage,
      alt: "Özel Havuzlu Termal Oda",
      title: "Özel Termal Havuzlu Odalar"
    },
    {
      src: spaCenterImage,
      alt: "Spa ve Wellness Merkezi",
      title: "Spa & Wellness Merkezi"
    },
    {
      src: restaurantImage,
      alt: "Restoran ve Yemek Alanı",
      title: "Restoran"
    },
    {
      src: hotelLobbyImage,
      alt: "Otel Lobisi",
      title: "Otel Lobisi"
    },
    {
      src: outdoorThermalImage,
      alt: "Açık Termal Alanlar",
      title: "Doğal Termal Alanları"
    }
  ];

  return (
    <section id="galeri" className="py-20 bg-spa">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4">
            Galeri
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-primary mb-6">
            Grand Neva'yı Keşfedin
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Lüks konaklamanız, şifalı termal deneyiminiz ve unutulmaz anlarınızdan kareler
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {galleryImages.map((image, index) => (
            <Card 
              key={index} 
              className="group cursor-pointer overflow-hidden hover:shadow-luxury transition-all duration-300 hover:scale-105"
              onClick={() => setSelectedImage(image.src)}
            >
              <div className="relative h-64 overflow-hidden">
                <img 
                  src={image.src}
                  alt={image.alt}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-primary/80 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="absolute bottom-4 left-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <h4 className="font-semibold text-sm">{image.title}</h4>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Lightbox Modal */}
        {selectedImage && (
          <div 
            className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedImage(null)}
          >
            <div className="relative max-w-4xl max-h-full">
              <button
                className="absolute top-4 right-4 text-white hover:text-accent transition-colors z-10"
                onClick={() => setSelectedImage(null)}
              >
                <X className="w-8 h-8" />
              </button>
              <img 
                src={selectedImage}
                alt="Gallery Image"
                className="max-w-full max-h-full object-contain rounded-lg"
              />
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default GallerySection;