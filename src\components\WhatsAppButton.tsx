import { MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

const WhatsAppButton = () => {
  const handleWhatsAppClick = () => {
    const phoneNumber = "905533141063";
    const message = "Merhaba! Grand Neva Oteli hakkında bilgi almak istiyorum.";
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <Button
      onClick={handleWhatsAppClick}
      variant="whatsapp"
      size="lg"
      className="fixed bottom-4 right-4 z-50 shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse md:hidden rounded-full w-14 h-14 p-0"
    >
      <MessageCircle className="h-6 w-6" />
    </Button>
  );
};

export default WhatsAppButton;