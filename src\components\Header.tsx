import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Menu, X, Phone, MessageCircle } from "lucide-react";
import logoImage from "@/assets/logo.png";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { name: "<PERSON> Sayfa", href: "#anasayfa" },
    { name: "Hakkımızda", href: "#hakkimizda" },
    { name: "<PERSON>zmetler<PERSON><PERSON>", href: "#hizmetler" },
    { name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", href: "#odalar" },
    { name: "<PERSON><PERSON>", href: "#galeri" },
    { name: "Blog", href: "#blog" },
    { name: "İletişim", href: "#iletisim" }
  ];

  const handleNavClick = (href: string) => {
    setIsMenuOpen(false);
    if (href === "#anasayfa") {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      const element = document.querySelector(href);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  const handleWhatsAppClick = () => {
    const phoneNumber = "905533141063";
    const message = "Merhaba! Grand Neva Oteli hakkında bilgi almak istiyorum.";
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled 
        ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-border' 
        : 'bg-transparent'
    }`}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <div className="flex items-center gap-3 cursor-pointer" onClick={() => handleNavClick("#anasayfa")}>
            <img 
              src={logoImage} 
              alt="Grand Neva Logo" 
              className="h-12 w-12 rounded-full shadow-md"
            />
            <div>
              <h1 className={`text-xl font-bold transition-colors ${
                isScrolled ? 'text-primary' : 'text-white'
              }`}>
                GRAND NEVA
              </h1>
              <p className="text-accent text-sm font-medium">TERMAL OTEL</p>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navItems.map((item, index) => (
              <button
                key={index}
                onClick={() => handleNavClick(item.href)}
                className={`text-sm font-medium transition-colors hover:text-accent ${
                  isScrolled ? 'text-foreground' : 'text-white'
                }`}
              >
                {item.name}
              </button>
            ))}
          </nav>

          {/* Contact Buttons */}
          <div className="hidden md:flex items-center gap-3">
            <Badge variant="secondary" className="hidden lg:flex items-center gap-1">
              <Phone className="w-3 h-3" />
              0272 772 08 42
            </Badge>
            <Button 
              variant="whatsapp" 
              size="sm"
              onClick={handleWhatsAppClick}
              className="shadow-md hover:shadow-lg transition-all"
            >
              <MessageCircle className="w-4 h-4 mr-1" />
              WhatsApp
            </Button>
            <Button 
              variant={isScrolled ? "hero" : "luxury"} 
              size="sm"
              onClick={() => handleNavClick("#iletisim")}
              className="shadow-md hover:shadow-lg transition-all"
            >
              Rezervasyon
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className={`lg:hidden p-2 rounded-md transition-colors ${
              isScrolled ? 'text-foreground hover:bg-muted' : 'text-white hover:bg-white/20'
            }`}
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white/95 backdrop-blur-md rounded-lg mt-2 shadow-lg border border-border">
              {navItems.map((item, index) => (
                <button
                  key={index}
                  onClick={() => handleNavClick(item.href)}
                  className="block w-full text-left px-3 py-2 text-base font-medium text-foreground hover:text-accent hover:bg-muted rounded-md transition-colors"
                >
                  {item.name}
                </button>
              ))}
              <div className="flex flex-col gap-2 pt-3 mt-3 border-t border-border">
                <Button 
                  variant="whatsapp" 
                  size="sm"
                  onClick={handleWhatsAppClick}
                  className="w-full"
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
                  WhatsApp: 0553 314 10 63
                </Button>
                <Button 
                  variant="hero" 
                  size="sm"
                  onClick={() => handleNavClick("#iletisim")}
                  className="w-full"
                >
                  Hemen Rezervasyon Yap
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;