import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { MapPin, Phone, Mail, Clock, MessageCircle } from "lucide-react";

const ContactSection = () => {
  const handleWhatsAppClick = () => {
    const phoneNumber = "905533141063";
    const message = "Merhaba! Grand Neva Oteli hakkında bilgi almak istiyorum.";
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  const handlePhoneClick = () => {
    window.open('tel:+902727720842', '_self');
  };

  const handleEmailClick = () => {
    window.open('mailto:<EMAIL>', '_self');
  };

  return (
    <section id="iletisim" className="py-20 bg-primary">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4 border-white text-white">
            İletişim
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Rezervasyon & İletişim
          </h2>
          <p className="text-lg text-white/90 max-w-3xl mx-auto">
            Termal tatil planlarınız için bize ulaşın. Size en uygun paketleri ve 
            kampanyaları önermeye hazırız.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Contact Info Cards */}
          <div className="space-y-6">
            <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20 transition-all duration-300">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-accent rounded-lg">
                    <MapPin className="w-6 h-6 text-accent-foreground" />
                  </div>
                  <CardTitle className="text-lg">Adres</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-white/90 text-sm leading-relaxed">
                  ESENTEPE MAH. GÜNDOĞAN CAD.<br />
                  NO: 5 YAYLA TERMAL YAYLABAĞI KASABASI<br />
                  03370 İhsaniye/Afyonkarahisar
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20 transition-all duration-300">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-accent rounded-lg">
                    <Phone className="w-6 h-6 text-accent-foreground" />
                  </div>
                  <CardTitle className="text-lg">Telefon</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <Button 
                  variant="link" 
                  className="text-white p-0 h-auto font-normal"
                  onClick={handlePhoneClick}
                >
                  +90 272 772 08 42
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20 transition-all duration-300">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-accent rounded-lg">
                    <Mail className="w-6 h-6 text-accent-foreground" />
                  </div>
                  <CardTitle className="text-lg">E-Posta</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <Button 
                  variant="link" 
                  className="text-white p-0 h-auto font-normal"
                  onClick={handleEmailClick}
                >
                  <EMAIL>
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20 transition-all duration-300">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-accent rounded-lg">
                    <Clock className="w-6 h-6 text-accent-foreground" />
                  </div>
                  <CardTitle className="text-lg">Çalışma Saatleri</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-white/90 text-sm">
                  7/24 Rezervasyon Hattı<br />
                  Resepsiyon: 24 Saat Açık
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-2">
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardHeader>
                <CardTitle className="text-2xl text-white text-center">
                  Hızlı Rezervasyon Formu
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input 
                    placeholder="Adınız Soyadınız" 
                    className="bg-white/20 border-white/30 text-white placeholder:text-white/70"
                  />
                  <Input 
                    placeholder="Telefon Numaranız" 
                    className="bg-white/20 border-white/30 text-white placeholder:text-white/70"
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input 
                    type="email"
                    placeholder="E-posta Adresiniz" 
                    className="bg-white/20 border-white/30 text-white placeholder:text-white/70"
                  />
                  <Input 
                    type="date"
                    placeholder="Giriş Tarihi" 
                    className="bg-white/20 border-white/30 text-white placeholder:text-white/70"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input 
                    type="number"
                    placeholder="Kişi Sayısı" 
                    className="bg-white/20 border-white/30 text-white placeholder:text-white/70"
                  />
                  <Input 
                    type="number"
                    placeholder="Gece Sayısı" 
                    className="bg-white/20 border-white/30 text-white placeholder:text-white/70"
                  />
                </div>

                <Textarea 
                  placeholder="Özel istekleriniz varsa buraya yazabilirsiniz..."
                  className="bg-white/20 border-white/30 text-white placeholder:text-white/70 min-h-[100px]"
                />

                <div className="flex flex-col sm:flex-row gap-4">
                  <Button 
                    variant="luxury" 
                    size="lg" 
                    className="flex-1"
                  >
                    Rezervasyon Talep Et
                  </Button>
                  <Button 
                    variant="whatsapp" 
                    size="lg"
                    onClick={handleWhatsAppClick}
                    className="flex-1"
                  >
                    <MessageCircle className="w-5 h-5 mr-2" />
                    WhatsApp ile İletişim
                  </Button>
                </div>

                <div className="text-center text-white/80 text-sm">
                  <p>* Rezervasyon talebiniz 24 saat içinde yanıtlanacaktır</p>
                  <p>* Fiyat bilgisi için lütfen bizimle iletişime geçiniz</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Quick Contact Buttons */}
        <div className="mt-12 text-center">
          <div className="flex flex-wrap justify-center gap-4">
            <Button 
              variant="whatsapp" 
              size="lg"
              onClick={handleWhatsAppClick}
              className="shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <MessageCircle className="w-5 h-5 mr-2" />
              WhatsApp: 0553 314 10 63
            </Button>
            <Button 
              variant="thermal" 
              size="lg"
              onClick={handlePhoneClick}
              className="shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <Phone className="w-5 h-5 mr-2" />
              Hemen Ara: 0272 772 08 42
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;