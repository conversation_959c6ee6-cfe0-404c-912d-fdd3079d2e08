import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Bed, Bath, Car, Wifi, Utensils, Tv, Wind } from "lucide-react";
import roomThermalImage from "@/assets/thermal-pool-room.jpg";

const RoomsSection = () => {
  const roomFeatures = [
    { icon: <Bath className="w-5 h-5" />, text: "2.5-3 ton özel termal havuz" },
    { icon: <Bath className="w-5 h-5" />, text: "Türk hamamı (kurna)" },
    { icon: <Bed className="w-5 h-5" />, text: "Konforlu yatak odası" },
    { icon: <Utensils className="w-5 h-5" />, text: "Mini mutfak" },
    { icon: <Tv className="w-5 h-5" />, text: "LED TV & uydu" },
    { icon: <Wind className="w-5 h-5" />, text: "<PERSON><PERSON><PERSON>" },
    { icon: <Wifi className="w-5 h-5" />, text: "Ücretsiz WiFi" },
    { icon: <Car className="w-5 h-5" />, text: "24 saat oda servisi" }
  ];

  const mealTimes = [
    { time: "08:30-10:30", meal: "Kahvaltı", type: "Açık Büfe", price: "Dahil" },
    { time: "13:00-14:00", meal: "Öğle Yemeği", type: "Alakart Restaurant", price: "Ekstra Ücretli" },
    { time: "15:00-15:45", meal: "Çay Saati", type: "İkramlar", price: "Dahil" },
    { time: "19:00-20:30", meal: "Akşam Yemeği", type: "Açık Büfe", price: "Dahil" }
  ];

  return (
    <section id="odalar" className="py-20 bg-card">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4">
            Konaklama
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-primary mb-6">
            Özel Havuzlu 1+1 Daireler
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Modern tasarım ve futbol temasıyla döşenmiş 65 m² dairelerimizde, 
            kendi özel termal havuzunuzda şifalı sularda dinlenin.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Room Image */}
          <div className="relative">
            <div className="rounded-2xl overflow-hidden shadow-luxury">
              <img 
                src={roomThermalImage} 
                alt="Özel Havuzlu Termal Daire"
                className="w-full h-96 object-cover"
              />
            </div>
            <div className="absolute -bottom-6 -right-6 bg-accent text-accent-foreground rounded-2xl p-4 shadow-lg">
              <div className="text-2xl font-bold">65m²</div>
              <div className="text-sm">Daire Alanı</div>
            </div>
          </div>

          {/* Room Features */}
          <div>
            <h3 className="text-3xl font-bold text-primary mb-6">
              Her Odada Size Özel Termal Deneyimi
            </h3>
            <p className="text-muted-foreground mb-8">
              Futbol temalı dekorasyonumuz ve lüks amenitylerimizle, 
              termal suyun şifalı gücünü odanızın konforunda yaşayın.
            </p>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
              {roomFeatures.map((feature, index) => (
                <div key={index} className="flex items-center gap-3 text-muted-foreground">
                  <div className="text-accent">
                    {feature.icon}
                  </div>
                  <span>{feature.text}</span>
                </div>
              ))}
            </div>

            <div className="bg-thermal rounded-lg p-6 mb-6">
              <h4 className="font-semibold text-thermal-foreground mb-2">Özel Özellikler:</h4>
              <ul className="text-thermal-foreground space-y-1">
                <li>• Termal yerden ısıtma sistemi (klima yok)</li>
                <li>• Her odaya özel termal su beslemesi</li>
                <li>• Futbol temalı dekorasyon</li>
                <li>• Özel tasarım mobilyalar</li>
              </ul>
            </div>

            <Button variant="hero" size="lg" className="w-full">
              Odalarımızı İnceleyin
            </Button>
          </div>
        </div>

        {/* Meal Schedule */}
        <div className="bg-spa rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-primary mb-4">
              Yarım Pansiyon Plus Konsept
            </h3>
            <p className="text-muted-foreground">
              Sağlıklı ve lezzetli yemek seçenekleriyle tatil keyfini yaşayın
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {mealTimes.map((meal, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <CardTitle className="text-accent text-lg">{meal.time}</CardTitle>
                </CardHeader>
                <CardContent>
                  <h4 className="font-semibold text-primary mb-1">{meal.meal}</h4>
                  <p className="text-sm text-muted-foreground mb-2">{meal.type}</p>
                  <Badge 
                    variant={meal.price === "Dahil" ? "secondary" : "outline"}
                    className="text-xs"
                  >
                    {meal.price}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="mt-6 text-center text-sm text-muted-foreground">
            <p>* Kahvaltı ve akşam yemeğinde içecekler ücretsizdir</p>
            <p>* Diyet, vegan ve çocuk menüsü seçenekleri mevcuttur</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RoomsSection;