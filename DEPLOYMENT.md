# Caprover Deployment Rehberi

Bu rehber, Neva Termal Escape projesini Caprover ile nasıl deploy edeceğinizi açıklar.

## Ön Gere<PERSON>

1. **Caprover CLI kurulumu:**
   ```bash
   npm install -g caprover
   ```

2. **Caprover server'ınızın hazır olması**
   - Caprover server'ınız çalışır durumda olmalı
   - Server URL'niz ve login bilgileriniz hazır olmalı

## Deployment Adımları

### 1. İlk Kurulum

Caprover CLI'yi server'ınıza bağlayın:
```bash
caprover serversetup
```

Bu komut sizden şunları isteyecek:
- Server URL (örn: https://captain.yourdomain.com)
- Email
- Password

### 2. Uygulama Oluşturma

Caprover dashboard'unda yeni bir uygulama oluşturun:
1. Caprover dashboard'a giriş yapın
2. "Apps" sekmesine gidin
3. "Create New App" butonuna tıklayın
4. App adını girin (örn: `neva-termal-escape`)

### 3. Deployment

#### Otomatik Deployment (Önerilen)

**Linux/Mac için:**
```bash
chmod +x deploy.sh
./deploy.sh
```

**Windows için:**
```cmd
deploy.bat
```

#### Manuel Deployment

```bash
# Build işlemi
npm run build

# Deploy
npm run deploy
```

veya

```bash
caprover deploy
```

## Dosya Yapısı

Deployment için oluşturulan dosyalar:

- `captain-definition` - Caprover konfigürasyon dosyası
- `Dockerfile` - Docker container tanımı
- `nginx.conf` - Nginx web server konfigürasyonu
- `caprover.json` - Caprover app konfigürasyonu
- `deploy.sh` - Linux/Mac deployment script'i
- `deploy.bat` - Windows deployment script'i
- `.dockerignore` - Docker build optimizasyonu

## Konfigürasyon

### Environment Variables

Caprover dashboard'unda environment variable'ları ayarlayabilirsiniz:
1. App'inizi seçin
2. "App Configs" sekmesine gidin
3. "Environment Variables" bölümünde değişkenlerinizi ekleyin

### Domain Ayarları

1. Caprover dashboard'da app'inizi seçin
2. "HTTP Settings" sekmesine gidin
3. "Enable HTTPS" seçeneğini aktifleştirin
4. Custom domain eklemek için "Connect New Domain" butonunu kullanın

## Troubleshooting

### Build Hatası
```bash
# Dependencies'leri temizle ve yeniden yükle
rm -rf node_modules package-lock.json
npm install
npm run build
```

### Deployment Hatası
```bash
# Caprover login durumunu kontrol et
caprover list

# Yeniden login
caprover login
```

### Container Logları
Caprover dashboard'da "App Logs" sekmesinden container loglarını inceleyebilirsiniz.

## Güncelleme

Kod değişikliklerinden sonra:
```bash
./deploy.sh
```

Bu komut otomatik olarak:
1. Projeyi build eder
2. Yeni version'ı deploy eder
3. Eski container'ı kapatır
4. Yeni container'ı başlatır

## Performans Optimizasyonları

- Nginx gzip compression aktif
- Static asset caching (1 yıl)
- Security headers eklendi
- Health check endpoint (`/health`)

## Destek

Deployment ile ilgili sorunlar için:
1. Caprover loglarını kontrol edin
2. Browser developer tools'da network tab'ını inceleyin
3. Nginx error loglarını kontrol edin
