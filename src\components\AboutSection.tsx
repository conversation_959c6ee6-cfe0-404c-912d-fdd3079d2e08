import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Check, Thermometer, MapPin, Clock } from "lucide-react";
import outdoorThermalImage from "@/assets/outdoor-thermal.jpg";

const AboutSection = () => {
  const features = [
    "Yalnızca size özel termal havuzlu odalar",
    "Gazlıgöl'ün şifalı suyu doğrudan odanıza ulaşır", 
    "<PERSON><PERSON> dostu, çocuklar için güvenli alanlar",
    "Termal sağlık, huzur ve hijyen bir arada",
    "Şehir stresinden uzak, doğa ile iç içe"
  ];

  const thermalBenefits = [
    "Romatizma ve eklem ağrıları",
    "Cilt hastalıkları",
    "Sinir sistemi bozuklukları", 
    "Stres ve yorgunluk"
  ];

  return (
    <section id="hakkimizda" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div>
            <Badge variant="secondary" className="mb-4">
              Hakkımızda
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-primary mb-6 leading-tight">
              Şifanın Kalbinde: Grand Neva Termal Otel'e Hoş Geldiniz
            </h2>
            <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
              Doğanın şifası, konforun zarafeti ve futbol temasının heyecanı...
              Afyonkarahisar Gazlıgöl'ün kalbinde, Frigya Vadisi'nin kadim atmosferine 
              komşu olan Grand Neva Termal Otel, sizi yepyeni bir termal tatil deneyimine davet ediyor.
            </p>

            <div className="space-y-4 mb-8">
              <h3 className="text-2xl font-semibold text-primary flex items-center gap-2">
                <Thermometer className="w-6 h-6 text-accent" />
                Termalin Şifasıyla Yenilenin
              </h3>
              <p className="text-muted-foreground">
                Otelimizin temelini oluşturan Gazlıgöl Termal Suyu, 74°C sıcaklık, 
                zengin mineralli içeriği ve Fransa'nın ünlü Whisy kaplıcalarıyla benzerliğiyle tanınır.
              </p>
              
              <div className="grid grid-cols-2 gap-4">
                {thermalBenefits.map((benefit, index) => (
                  <div key={index} className="flex items-center text-sm">
                    <Check className="w-4 h-4 text-accent mr-2 flex-shrink-0" />
                    <span>{benefit}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-primary">Neden Grand Neva Termal Otel?</h3>
              <div className="space-y-3">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-accent rounded-full flex items-center justify-center mt-0.5">
                      <Check className="w-4 h-4 text-accent-foreground" />
                    </div>
                    <span className="text-muted-foreground">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Content - Image & Info Cards */}
          <div className="space-y-6">
            {/* Outdoor Thermal Image */}
            <div className="relative rounded-2xl overflow-hidden shadow-luxury">
              <img 
                src={outdoorThermalImage} 
                alt="Grand Neva Termal Otel Doğal Termal Alanları"
                className="w-full h-64 object-cover"
              />
              <div className="absolute inset-0 bg-primary/60"></div>
              <div className="absolute bottom-4 left-4 text-white">
                <h4 className="text-xl font-bold">Doğal Termal Deneyimi</h4>
                <p className="text-sm text-white/90">Gazlıgöl'ün şifalı sularında huzur</p>
              </div>
            </div>
            <Card className="bg-primary text-white p-6 shadow-luxury">
              <CardContent className="p-0">
                <div className="flex items-center gap-4 mb-4">
                  <MapPin className="w-8 h-8 text-accent" />
                  <div>
                    <h3 className="text-2xl font-bold">Tarihi Konum</h3>
                    <p className="text-white/80">Frigya Vadisi Yakınında</p>
                  </div>
                </div>
                <p className="text-white/90">
                  Frigya Vadisi, Ayazini Kaya Yerleşimi, Emre Gölü ve tarihi kalıntılara 
                  sadece dakikalar uzaklıktadır.
                </p>
              </CardContent>
            </Card>

            <div className="grid grid-cols-2 gap-4">
              <Card className="bg-thermal p-6 text-center">
                <CardContent className="p-0">
                  <div className="text-3xl font-bold text-thermal-foreground mb-2">74°C</div>
                  <div className="text-sm text-thermal-foreground">Termal Su Sıcaklığı</div>
                </CardContent>
              </Card>
              
              <Card className="bg-accent p-6 text-center">
                <CardContent className="p-0">
                  <div className="text-3xl font-bold text-accent-foreground mb-2">65m²</div>
                  <div className="text-sm text-accent-foreground">Daire Büyüklüğü</div>
                </CardContent>
              </Card>
            </div>

            <Card className="bg-secondary p-6 shadow-thermal">
              <CardContent className="p-0">
                <div className="flex items-center gap-4 mb-4">
                  <Clock className="w-8 h-8 text-white" />
                  <div>
                    <h3 className="text-2xl font-bold text-white">24 Saat</h3>
                    <p className="text-white/80">Oda Servisi</p>
                  </div>
                </div>
                <p className="text-white/90">
                  Konforlu konaklamanız için 7/24 hizmet desteği sunuyoruz.
                </p>
              </CardContent>
            </Card>

            <Button 
              variant="hero" 
              size="lg" 
              className="w-full"
              onClick={() => document.getElementById('iletisim')?.scrollIntoView({ behavior: 'smooth' })}
            >
              Rezervasyon İçin İletişime Geçin
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;